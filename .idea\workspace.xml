<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5c5c1b16-ba9f-4d80-baa1-838f8cc55030" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/ChisZyjkLiteflowAlarmApiApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/config/LiteFlowConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/controller/AlertRuleController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/controller/LiteFlowController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/enums/ProcessStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/common/ApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/common/NodeConfigHelper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/common/NodeDataHelper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/exception/LiteFlowErrorCode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/exception/LiteFlowException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/exception/LiteFlowExceptionHelper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/exception/LiteFlowNodeException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/exception/LiteFlowRuleException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/executor/LiteFlowRuleExecutor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/AlertCreateNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/ApiDataBatchFetchNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/ApiDataFetchNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/FilterJudgeNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/GetAlertRecordNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/IfJudgeNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/nodes/NotificationNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/pojo/BatchProcessResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/pojo/FilterResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/pojo/NotificationItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/pojo/NotificationResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/utils/BaseApiNodeUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/utils/ConditionEvaluatorUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/liteflow/utils/VariableReplacerUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/mapper/AlertRecordLogMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/mapper/AlertRecordMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/mapper/AlertRecordNoticeLogMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/mapper/AlertRuleMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/AlertRuleCreateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/AlertRuleQueryRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/AlertRuleResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/AlertRuleUpdateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/ExecuteRuleRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/TestNodeRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/dto/ValidateExpressionRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/AlertRecordLogPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/AlertRecordNoticeLogPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/AlertRecordPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/AlertRuleArchivePO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/AlertRuleLevelPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/AlertRulePO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/LiteflowExecutionLogPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/LiteflowNodeExecutionLogPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/pojo/po/XxljobExecutionLogPO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/service/AlertRecordLogService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/service/AlertRecordNoticeLogService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/service/AlertRecordService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/java/com/chis/zyjk/api/liteflow/alarm/service/AlertRuleService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/resources/bootstrap.yaml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/resources/mapper/AlertRecordLogMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/resources/mapper/AlertRecordMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/resources/mapper/AlertRecordNoticeLogMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-api-liteflow-alarm/src/main/resources/mapper/AlertRuleMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/api/liteflow/alertRule.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/alert-rule-management/index.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/components/Canvas.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/components/CodePreview.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/components/ComponentPanel.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/components/ConnectionLine.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/components/DragNode.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/components/PropertyPanel.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/index.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/stores/ruleEditor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/chis-zyjk-web/src/views/liteflow/rule-editor/utils/common.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/database-design.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/chis-zyjk-web/.env.development" beforeDir="false" afterPath="$PROJECT_DIR$/chis-zyjk-web/.env.development" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/chis-zyjk-web/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/chis-zyjk-web/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/chis-zyjk-web/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/chis-zyjk-web/pnpm-lock.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/chis-zyjk-web/public/config.json" beforeDir="false" afterPath="$PROJECT_DIR$/chis-zyjk-web/public/config.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
        <option value="Enum" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/chis-zyjk-web" value="dev_2.0.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\develop\maven\apache-maven-3.6.1" />
        <option name="localRepository" value="E:\develop\maven\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\develop\maven\setting\zyjk\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyEnabledProfiles" value="chisframe,aliyun" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/chis-zyjk-web/package.json" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zyrW0oEherCs1usC9s664cPpJE" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.chis-zyjk-api-liteflow-alarm [clean].executor": "Run",
    "Maven.chis-zyjk-api-liteflow-alarm [install].executor": "Run",
    "Maven.chis-zyjk-core-common [clean].executor": "Run",
    "Maven.chis-zyjk-core-common [install].executor": "Run",
    "Maven.chis-zyjk-core-common [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "找到数据库连接形参",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "Spring Boot.ChisZyjkLiteflowAlarm.executor": "Debug",
    "Spring Boot.ChisZyjkLiteflowAlarmApiApplication.executor": "Debug",
    "Spring Boot.LiteflowExampleApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "E:/work/ai/liteflow",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Debug",
    "npm.install.executor": "Run",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "F:\\JetBrains\\app\\IntelliJ IDEA Ultimate\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\work\ai\liteflow" />
      <recent name="E:\work\ai\liteflow\liteflow-alarm\src\main\resources" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.chis.zyjk.api.liteflow.alarm.liteflow.nodes" />
      <recent name="com.chis.zyjk.api.liteflow.alarm.controller" />
      <recent name="com.chis.zyjk.api.liteflow.alarm" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="AntRunConfiguration" />
        <option value="Application" />
        <option value="BatchConfigurationType" />
        <option value="ChromiumRemoteDebugType" />
        <option value="CompoundRunConfigurationType" />
        <option value="DatabaseScript" />
        <option value="GradleRunConfiguration" />
        <option value="GroovyScriptRunConfiguration" />
        <option value="JUnit" />
        <option value="JarApplication" />
        <option value="Java Scratch" />
        <option value="JavaScriptTestRunnerCypress" />
        <option value="JavaScriptTestRunnerJest" />
        <option value="JavaScriptTestRunnerKarma" />
        <option value="JavaScriptTestRunnerPlaywright" />
        <option value="JavaScriptTestRunnerProtractor" />
        <option value="JavaScriptTestRunnerVitest" />
        <option value="JavascriptDebugType" />
        <option value="JetRunConfigurationType" />
        <option value="KotlinStandaloneScriptRunConfigurationType" />
        <option value="KtorApplicationConfigurationType" />
        <option value="MavenRunConfiguration" />
        <option value="MicronautRunConfigurationType" />
        <option value="NodeJSConfigurationType" />
        <option value="NodeJsTestRunner" />
        <option value="NodeWebKit" />
        <option value="NodeunitConfigurationType" />
        <option value="OpenRewriteRunConfigurationType" />
        <option value="QsApplicationConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="ReactNative" />
        <option value="Remote" />
        <option value="ShConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
        <option value="SwaggerCodegen" />
        <option value="TSqltConfigurationType" />
        <option value="TailRunConfiguration" />
        <option value="TestNG" />
        <option value="UtPlSqlConfigurationType" />
        <option value="XSLT" />
        <option value="docker-deploy" />
        <option value="js.build_tools.grunt" />
        <option value="js.build_tools.gulp" />
        <option value="js.build_tools.npm" />
        <option value="mocha-javascript-test-runner" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="npm.install">
    <configuration name="ChisZyjkLiteflowAlarm" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true">
      <module name="chis-zyjk-api-liteflow-alarm" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chis.zyjk.api.liteflow.alarm.ChisZyjkLiteflowAlarmApiApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chis.zyjk.api.liteflow.alarm.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LiteflowExampleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="liteflow-example" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yomahub.liteflow.example.LiteflowExampleApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/chis-zyjk-web/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="install" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/chis-zyjk-web/package.json" />
      <command value="install" />
      <node-interpreter value="project" />
      <package-manager value="pnpm" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="predev" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/chis-zyjk-web/package.json" />
      <command value="run" />
      <scripts>
        <script value="predev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.dev" />
      <item itemvalue="npm.install" />
      <item itemvalue="npm.predev" />
      <item itemvalue="Spring Boot.LiteflowExampleApplication" />
      <item itemvalue="Spring Boot.ChisZyjkLiteflowAlarm" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ChisZyjkLiteflowAlarm" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5c5c1b16-ba9f-4d80-baa1-838f8cc55030" name="更改" comment="" />
      <created>1752714182991</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752714182991</updated>
      <workItem from="1752714184400" duration="21002000" />
      <workItem from="1752801138852" duration="2564000" />
      <workItem from="1752803883289" duration="31833000" />
      <workItem from="1753063240730" duration="84453000" />
      <workItem from="1753324793362" duration="27545000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>